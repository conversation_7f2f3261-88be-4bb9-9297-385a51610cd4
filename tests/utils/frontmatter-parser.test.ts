import { describe, it, expect, beforeEach, vi } from 'vitest';
import { FrontmatterParser } from '../../src/utils/frontmatter-parser';

// Mock Obsidian API
const mockApp = {
  metadataCache: {
    getFileCache: vi.fn()
  }
};

const mockFile = {
  path: 'test-article.md',
  name: 'test-article.md'
};

describe('FrontmatterParser', () => {
  let parser: FrontmatterParser;

  beforeEach(() => {
    vi.clearAllMocks();
    parser = new FrontmatterParser(mockApp as any);
  });

  describe('Case Sensitivity Handling', () => {
    it('should handle lowercase frontmatter properties', async () => {
      const frontmatter = {
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        tags: ['test'],
        featured: false
      };

      mockApp.metadataCache.getFileCache.mockReturnValue({ frontmatter });

      const result = await parser.parse(mockFile as any);

      expect(result).toEqual({
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        tags: ['test'],
        featured: false,
        feature_image: undefined,
        visibility: undefined,
        primary_tag: undefined,
        created_at: undefined,
        updated_at: undefined,
        published_at: undefined,
        newsletter: undefined,
        email_sent: undefined,
        // Original properties preserved
        ...frontmatter
      });
    });

    it('should handle capitalized frontmatter properties', async () => {
      const frontmatter = {
        Title: 'Test Post',
        Slug: 'test-post',
        Status: 'draft',
        Tags: ['test'],
        Featured: false
      };

      mockApp.metadataCache.getFileCache.mockReturnValue({ frontmatter });

      const result = await parser.parse(mockFile as any);

      expect(result?.title).toBe('Test Post');
      expect(result?.slug).toBe('test-post');
      expect(result?.status).toBe('draft');
      expect(result?.tags).toEqual(['test']);
      expect(result?.featured).toBe(false);
    });

    it('should handle mixed case frontmatter properties', async () => {
      const frontmatter = {
        title: 'Test Post',
        Slug: 'test-post',  // Mixed case
        Status: 'draft',
        tags: ['test'],
        Featured: false
      };

      mockApp.metadataCache.getFileCache.mockReturnValue({ frontmatter });

      const result = await parser.parse(mockFile as any);

      expect(result?.title).toBe('Test Post');
      expect(result?.slug).toBe('test-post');
      expect(result?.status).toBe('draft');
      expect(result?.tags).toEqual(['test']);
      expect(result?.featured).toBe(false);
    });

    it('should prioritize lowercase over capitalized when both exist', async () => {
      const frontmatter = {
        title: 'Lowercase Title',
        Title: 'Capitalized Title',
        slug: 'lowercase-slug',
        Slug: 'capitalized-slug'
      };

      mockApp.metadataCache.getFileCache.mockReturnValue({ frontmatter });

      const result = await parser.parse(mockFile as any);

      expect(result?.title).toBe('Lowercase Title');
      expect(result?.slug).toBe('lowercase-slug');
    });
  });

  describe('Race Condition Handling', () => {
    it('should retry when frontmatter is initially undefined', async () => {
      const frontmatter = {
        slug: 'test-post',
        title: 'Test Post'
      };

      // First call returns undefined, second call returns frontmatter
      mockApp.metadataCache.getFileCache
        .mockReturnValueOnce(undefined)
        .mockReturnValueOnce({ frontmatter });

      const result = await parser.parse(mockFile as any);

      // Should have been called twice due to retry
      expect(mockApp.metadataCache.getFileCache).toHaveBeenCalledTimes(2);
      expect(result?.slug).toBe('test-post');
      expect(result?.title).toBe('Test Post');
    });

    it('should return null when frontmatter remains undefined after retry', async () => {
      mockApp.metadataCache.getFileCache.mockReturnValue(undefined);

      const result = await parser.parse(mockFile as any);

      // Should have been called twice due to retry
      expect(mockApp.metadataCache.getFileCache).toHaveBeenCalledTimes(2);
      expect(result).toBeNull();
    });

    it('should return null when file cache returns null', async () => {
      mockApp.metadataCache.getFileCache.mockReturnValue(null);

      const result = await parser.parse(mockFile as any);

      expect(result).toBeNull();
    });
  });

  describe('Slug Extraction', () => {
    it('should extract lowercase slug', () => {
      const frontmatter = { slug: 'test-post' };
      const result = parser.extractSlug(frontmatter);
      expect(result).toBe('test-post');
    });

    it('should extract capitalized slug', () => {
      const frontmatter = { Slug: 'test-post' };
      const result = parser.extractSlug(frontmatter);
      expect(result).toBe('test-post');
    });

    it('should prioritize lowercase slug over capitalized', () => {
      const frontmatter = { slug: 'lowercase-slug', Slug: 'capitalized-slug' };
      const result = parser.extractSlug(frontmatter);
      expect(result).toBe('lowercase-slug');
    });

    it('should trim whitespace from slug', () => {
      const frontmatter = { slug: '  test-post  ' };
      const result = parser.extractSlug(frontmatter);
      expect(result).toBe('test-post');
    });

    it('should return null for empty slug', () => {
      const frontmatter = { slug: '' };
      const result = parser.extractSlug(frontmatter);
      expect(result).toBeNull();
    });

    it('should return null for whitespace-only slug', () => {
      const frontmatter = { slug: '   ' };
      const result = parser.extractSlug(frontmatter);
      expect(result).toBeNull();
    });

    it('should return null when no slug property exists', () => {
      const frontmatter = { title: 'Test Post' };
      const result = parser.extractSlug(frontmatter);
      expect(result).toBeNull();
    });

    it('should return null for null frontmatter', () => {
      const result = parser.extractSlug(null);
      expect(result).toBeNull();
    });
  });

  describe('Slug Validation', () => {
    it('should return true for valid slug', () => {
      const frontmatter = { slug: 'test-post' };
      const result = parser.hasValidSlug(frontmatter);
      expect(result).toBe(true);
    });

    it('should return false for empty slug', () => {
      const frontmatter = { slug: '' };
      const result = parser.hasValidSlug(frontmatter);
      expect(result).toBe(false);
    });

    it('should return false for null frontmatter', () => {
      const result = parser.hasValidSlug(null);
      expect(result).toBe(false);
    });

    it('should return false when no slug exists', () => {
      const frontmatter = { title: 'Test Post' };
      const result = parser.hasValidSlug(frontmatter);
      expect(result).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('should handle null file', async () => {
      const result = await parser.parse(null);
      expect(result).toBeNull();
    });

    it('should handle metadata cache errors gracefully', async () => {
      mockApp.metadataCache.getFileCache.mockImplementation(() => {
        throw new Error('Metadata cache error');
      });

      const result = await parser.parse(mockFile as any);
      expect(result).toBeNull();
    });
  });
});
