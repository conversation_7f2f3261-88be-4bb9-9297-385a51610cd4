import { describe, it, expect, beforeEach } from 'vitest';
import { SyncStatusComparator } from '../../src/utils/sync-status-comparator';
import { ParsedFrontmatter } from '../../src/utils/frontmatter-parser';

describe('SyncStatusComparator', () => {
  let comparator: SyncStatusComparator;

  beforeEach(() => {
    comparator = new SyncStatusComparator();
  });

  describe('Value Comparison', () => {
    it('should return synced for identical values', () => {
      const frontmatter: ParsedFrontmatter = {
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft'
      };

      const ghostPost = {
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft'
      };

      const result = comparator.compare(frontmatter, ghostPost);

      expect(result.title).toBe('synced');
      expect(result.slug).toBe('synced');
      expect(result.status).toBe('synced');
    });

    it('should return different for different values', () => {
      const frontmatter: ParsedFrontmatter = {
        title: 'Local Title',
        slug: 'test-post',
        status: 'draft'
      };

      const ghostPost = {
        title: 'Ghost Title',
        slug: 'test-post',
        status: 'published'
      };

      const result = comparator.compare(frontmatter, ghostPost);

      expect(result.title).toBe('different');
      expect(result.slug).toBe('synced');
      expect(result.status).toBe('different');
    });

    it('should handle null and undefined values', () => {
      const frontmatter: ParsedFrontmatter = {
        title: 'Test Post',
        feature_image: null,
        primary_tag: undefined
      };

      const ghostPost = {
        title: 'Test Post',
        feature_image: null,
        primary_tag: null
      };

      const result = comparator.compare(frontmatter, ghostPost);

      expect(result.title).toBe('synced');
      expect(result.feature_image).toBe('unknown');
      expect(result.primary_tag).toBe('unknown');
    });

    it('should return different when one value is null and other is not', () => {
      const frontmatter: ParsedFrontmatter = {
        title: 'Test Post',
        feature_image: 'image.jpg'
      };

      const ghostPost = {
        title: 'Test Post',
        feature_image: null
      };

      const result = comparator.compare(frontmatter, ghostPost);

      expect(result.title).toBe('synced');
      expect(result.feature_image).toBe('different');
    });

    it('should trim whitespace when comparing', () => {
      const frontmatter: ParsedFrontmatter = {
        title: '  Test Post  ',
        slug: 'test-post'
      };

      const ghostPost = {
        title: 'Test Post',
        slug: 'test-post'
      };

      const result = comparator.compare(frontmatter, ghostPost);

      expect(result.title).toBe('synced');
    });
  });

  describe('Tags Comparison', () => {
    it('should return synced for identical tag arrays', () => {
      const frontmatter: ParsedFrontmatter = {
        tags: ['tag1', 'tag2', 'tag3']
      };

      const ghostPost = {
        tags: [
          { name: 'tag1' },
          { name: 'tag2' },
          { name: 'tag3' }
        ]
      };

      const result = comparator.compare(frontmatter, ghostPost);
      expect(result.tags).toBe('synced');
    });

    it('should return different for different tag arrays', () => {
      const frontmatter: ParsedFrontmatter = {
        tags: ['tag1', 'tag2']
      };

      const ghostPost = {
        tags: [
          { name: 'tag1' },
          { name: 'tag3' }
        ]
      };

      const result = comparator.compare(frontmatter, ghostPost);
      expect(result.tags).toBe('different');
    });

    it('should return different for different tag array lengths', () => {
      const frontmatter: ParsedFrontmatter = {
        tags: ['tag1', 'tag2']
      };

      const ghostPost = {
        tags: [
          { name: 'tag1' }
        ]
      };

      const result = comparator.compare(frontmatter, ghostPost);
      expect(result.tags).toBe('different');
    });

    it('should handle undefined tags', () => {
      const frontmatter: ParsedFrontmatter = {
        tags: undefined
      };

      const ghostPost = {
        tags: undefined
      };

      const result = comparator.compare(frontmatter, ghostPost);
      expect(result.tags).toBe('unknown');
    });

    it('should return different when one has tags and other does not', () => {
      const frontmatter: ParsedFrontmatter = {
        tags: ['tag1']
      };

      const ghostPost = {
        tags: undefined
      };

      const result = comparator.compare(frontmatter, ghostPost);
      expect(result.tags).toBe('different');
    });

    it('should sort tags before comparison', () => {
      const frontmatter: ParsedFrontmatter = {
        tags: ['tag2', 'tag1', 'tag3']
      };

      const ghostPost = {
        tags: [
          { name: 'tag1' },
          { name: 'tag3' },
          { name: 'tag2' }
        ]
      };

      const result = comparator.compare(frontmatter, ghostPost);
      expect(result.tags).toBe('synced');
    });
  });

  describe('Special Field Handling', () => {
    it('should handle primary_tag with nested name property', () => {
      const frontmatter: ParsedFrontmatter = {
        primary_tag: 'main-tag'
      };

      const ghostPost = {
        primary_tag: { name: 'main-tag' }
      };

      const result = comparator.compare(frontmatter, ghostPost);
      expect(result.primary_tag).toBe('synced');
    });

    it('should handle newsletter with nested name property', () => {
      const frontmatter: ParsedFrontmatter = {
        newsletter: 'weekly-update'
      };

      const ghostPost = {
        newsletter: { name: 'weekly-update' }
      };

      const result = comparator.compare(frontmatter, ghostPost);
      expect(result.newsletter).toBe('synced');
    });

    it('should handle email_sent field correctly', () => {
      const frontmatter: ParsedFrontmatter = {
        email_sent: 'Yes'
      };

      const ghostPost = {
        email: { id: 'some-email-id' }
      };

      const result = comparator.compare(frontmatter, ghostPost);
      expect(result.email_sent).toBe('synced');
    });

    it('should handle email_sent when no email was sent', () => {
      const frontmatter: ParsedFrontmatter = {
        email_sent: 'No'
      };

      const ghostPost = {
        email: null
      };

      const result = comparator.compare(frontmatter, ghostPost);
      expect(result.email_sent).toBe('synced');
    });
  });

  describe('Null Input Handling', () => {
    it('should return unknown status when frontmatter is null', () => {
      const ghostPost = { title: 'Test' };
      const result = comparator.compare(null, ghostPost);

      expect(result.title).toBe('unknown');
      expect(result.slug).toBe('unknown');
      expect(result.status).toBe('unknown');
      expect(result.ghostPost).toBe(ghostPost);
    });

    it('should return unknown status when ghostPost is null', () => {
      const frontmatter: ParsedFrontmatter = { title: 'Test' };
      const result = comparator.compare(frontmatter, null);

      expect(result.title).toBe('unknown');
      expect(result.slug).toBe('unknown');
      expect(result.status).toBe('unknown');
      expect(result.ghostPost).toBeNull();
    });

    it('should return unknown status when both are null', () => {
      const result = comparator.compare(null, null);

      expect(result.title).toBe('unknown');
      expect(result.slug).toBe('unknown');
      expect(result.status).toBe('unknown');
      expect(result.ghostPost).toBeNull();
    });
  });

  describe('Boolean Comparison', () => {
    it('should handle boolean values correctly', () => {
      const frontmatter: ParsedFrontmatter = {
        featured: true
      };

      const ghostPost = {
        featured: true
      };

      const result = comparator.compare(frontmatter, ghostPost);
      expect(result.featured).toBe('synced');
    });

    it('should detect boolean differences', () => {
      const frontmatter: ParsedFrontmatter = {
        featured: false
      };

      const ghostPost = {
        featured: true
      };

      const result = comparator.compare(frontmatter, ghostPost);
      expect(result.featured).toBe('different');
    });
  });
});
