import { App, TFile, CachedMetadata } from 'obsidian';

export interface ParsedFrontmatter {
  slug?: string;
  title?: string;
  status?: string;
  tags?: string[];
  featured?: boolean;
  feature_image?: string;
  visibility?: string;
  primary_tag?: string;
  created_at?: string;
  updated_at?: string;
  published_at?: string;
  newsletter?: string;
  email_sent?: string;
  [key: string]: any;
}

export class FrontmatterParser {
  constructor(private app: App) {}

  /**
   * Parse frontmatter from a file with case-insensitive property access and retry logic
   */
  async parse(file: TFile | null): Promise<ParsedFrontmatter | null> {
    if (!file) {
      return null;
    }

    try {
      // Get frontmatter from metadata cache
      let frontmatter = this.app.metadataCache.getFileCache(file)?.frontmatter;

      // If frontmatter is undefined, wait a bit and try again (metadata cache might be updating)
      if (!frontmatter) {
        await new Promise(resolve => setTimeout(resolve, 100));
        frontmatter = this.app.metadataCache.getFileCache(file)?.frontmatter;
      }

      if (!frontmatter) {
        return null;
      }

      // Create normalized frontmatter object with case-insensitive access
      return this.normalizeFrontmatter(frontmatter);
    } catch (error) {
      console.error('Error parsing frontmatter:', error);
      return null;
    }
  }

  /**
   * Normalize frontmatter properties to handle both lowercase and capitalized versions
   */
  private normalizeFrontmatter(frontmatter: any): ParsedFrontmatter {
    const getValue = (key: string) => {
      return frontmatter[key] || frontmatter[key.charAt(0).toUpperCase() + key.slice(1)];
    };

    return {
      slug: getValue('slug'),
      title: getValue('title'),
      status: getValue('status'),
      tags: getValue('tags'),
      featured: getValue('featured'),
      feature_image: getValue('feature_image'),
      visibility: getValue('visibility'),
      primary_tag: getValue('primary_tag'),
      created_at: getValue('created_at'),
      updated_at: getValue('updated_at'),
      published_at: getValue('published_at'),
      newsletter: getValue('newsletter'),
      email_sent: getValue('email_sent'),
      // Include any other properties as-is
      ...frontmatter
    };
  }

  /**
   * Extract slug from frontmatter, handling both cases
   */
  extractSlug(frontmatter: any): string | null {
    if (!frontmatter) {
      return null;
    }

    const slug = frontmatter.slug || frontmatter.Slug;
    return slug && slug.trim() ? slug.trim() : null;
  }

  /**
   * Check if frontmatter has a valid slug
   */
  hasValidSlug(frontmatter: ParsedFrontmatter | null): boolean {
    if (!frontmatter) {
      return false;
    }

    const slug = this.extractSlug(frontmatter);
    return slug !== null && slug.length > 0;
  }
}
