import { ParsedFrontmatter } from './frontmatter-parser';

export type SyncStatus = 'synced' | 'different' | 'unknown';

export interface SyncStatusResult {
  title: SyncStatus;
  slug: SyncStatus;
  status: SyncStatus;
  tags: SyncStatus;
  featured: SyncStatus;
  feature_image: SyncStatus;
  visibility: SyncStatus;
  primary_tag: SyncStatus;
  created_at: SyncStatus;
  updated_at: SyncStatus;
  published_at: SyncStatus;
  newsletter: SyncStatus;
  email_sent: SyncStatus;
  ghostPost?: any;
}

export class SyncStatusComparator {
  /**
   * Compare local frontmatter with Ghost post data
   */
  compare(frontmatter: ParsedFrontmatter | null, ghostPost: any | null): SyncStatusResult {
    if (!frontmatter || !ghostPost) {
      return this.createUnknownStatus(ghostPost);
    }

    return {
      title: this.compareValues(frontmatter.title, ghostPost.title),
      slug: this.compareValues(frontmatter.slug, ghostPost.slug),
      status: this.compareValues(frontmatter.status, ghostPost.status),
      tags: this.compareTags(frontmatter.tags, ghostPost.tags),
      featured: this.compareValues(frontmatter.featured, ghostPost.featured),
      feature_image: this.compareValues(frontmatter.feature_image, ghostPost.feature_image),
      visibility: this.compareValues(frontmatter.visibility, ghostPost.visibility),
      primary_tag: this.compareValues(frontmatter.primary_tag, ghostPost.primary_tag?.name),
      created_at: this.compareValues(frontmatter.created_at, ghostPost.created_at),
      updated_at: this.compareValues(frontmatter.updated_at, ghostPost.updated_at),
      published_at: this.compareValues(frontmatter.published_at, ghostPost.published_at),
      newsletter: this.compareValues(frontmatter.newsletter, ghostPost.newsletter?.name),
      email_sent: this.compareValues(frontmatter.email_sent, ghostPost.email ? 'Yes' : 'No'),
      ghostPost
    };
  }

  /**
   * Compare two values and return sync status
   */
  private compareValues(localValue: any, ghostValue: any): SyncStatus {
    // Handle null/undefined cases
    if (localValue == null && ghostValue == null) {
      return 'unknown';
    }
    
    if (localValue == null || ghostValue == null) {
      return localValue === ghostValue ? 'unknown' : 'different';
    }

    // Convert to strings for comparison
    const localStr = String(localValue).trim();
    const ghostStr = String(ghostValue).trim();

    if (localStr === ghostStr) {
      return 'synced';
    }

    return 'different';
  }

  /**
   * Compare tags arrays
   */
  private compareTags(localTags: string[] | undefined, ghostTags: any[] | undefined): SyncStatus {
    if (!localTags && !ghostTags) {
      return 'unknown';
    }

    if (!localTags || !ghostTags) {
      return 'different';
    }

    // Convert Ghost tags to string array
    const ghostTagNames = ghostTags.map(tag => tag.name || tag).sort();
    const localTagNames = localTags.sort();

    if (localTagNames.length !== ghostTagNames.length) {
      return 'different';
    }

    for (let i = 0; i < localTagNames.length; i++) {
      if (localTagNames[i] !== ghostTagNames[i]) {
        return 'different';
      }
    }

    return 'synced';
  }

  /**
   * Create a status result with all unknown values
   */
  private createUnknownStatus(ghostPost?: any): SyncStatusResult {
    return {
      title: 'unknown',
      slug: 'unknown',
      status: 'unknown',
      tags: 'unknown',
      featured: 'unknown',
      feature_image: 'unknown',
      visibility: 'unknown',
      primary_tag: 'unknown',
      created_at: 'unknown',
      updated_at: 'unknown',
      published_at: 'unknown',
      newsletter: 'unknown',
      email_sent: 'unknown',
      ghostPost
    };
  }
}
