import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SvelteSyncStatusView } from '../../src/views/svelte-sync-status-view';
import { SyncStatusService } from '../../src/services/sync-status-service';
import { WorkspaceLeaf, TFile } from 'obsidian';

// Mock SyncStatusService
const mockSyncStatusService = {
  calculateSyncStatus: vi.fn(),
  isValidForSync: vi.fn(),
  extractFrontmatterValues: vi.fn()
} as jest.Mocked<SyncStatusService>;

const mockPlugin = {
  settings: {
    ghostUrl: 'https://test.ghost.io',
    ghostAdminApiKey: '123456789012345678901234:1234567890123456789012345678901234567890123456789012345678901234'
  },
  app: {
    workspace: {
      getActiveFile: vi.fn(),
      on: vi.fn(),
      off: vi.fn()
    },
    vault: {
      read: vi.fn(),
      modify: vi.fn(),
      on: vi.fn(),
      off: vi.fn()
    },
    metadataCache: {
      getFileCache: vi.fn(),
      on: vi.fn(),
      off: vi.fn()
    }
  }
};

// Mock Svelte components
vi.mock('../../src/components/GhostSyncView.svelte', () => ({
  default: vi.fn().mockImplementation(() => ({
    $destroy: vi.fn(),
    $on: vi.fn()
  }))
}));

vi.mock('../../src/components/PublishDialog.svelte', () => ({
  default: vi.fn()
}));

vi.mock('../../src/components/PostBrowser.svelte', () => ({
  default: vi.fn()
}));

describe('SvelteSyncStatusView', () => {
  let view: SvelteSyncStatusView;
  let mockFile: TFile;
  let mockLeaf: WorkspaceLeaf;

  beforeEach(() => {
    vi.clearAllMocks();

    mockFile = new TFile('test-article.md');

    mockLeaf = {
      view: null,
      getViewState: vi.fn(),
      setViewState: vi.fn(),
      on: vi.fn(),
      off: vi.fn()
    } as any;

    view = new SvelteSyncStatusView(mockLeaf, mockPlugin as any, mockSyncStatusService);

    // Mock the contentEl property
    (view as any).contentEl = {
      empty: vi.fn(),
      addClass: vi.fn(),
      createEl: vi.fn(() => ({ $on: vi.fn(), $destroy: vi.fn() }))
    };

    // Mock the app property
    (view as any).app = mockPlugin.app;

    (view as any).component = null;
  });

  describe('Sync Status Updates', () => {
    it('should call sync status service when updating status', async () => {
      const mockSyncStatus = {
        title: 'synced',
        slug: 'synced',
        status: 'synced',
        tags: 'synced',
        featured: 'synced',
        feature_image: 'synced',
        visibility: 'synced',
        primary_tag: 'synced',
        created_at: 'synced',
        updated_at: 'synced',
        published_at: 'synced',
        newsletter: 'synced',
        email_sent: 'synced'
      };

      mockSyncStatusService.calculateSyncStatus.mockResolvedValue(mockSyncStatus);

      (view as any).currentFile = mockFile;
      await (view as any).updateSyncStatus();

      expect(mockSyncStatusService.calculateSyncStatus).toHaveBeenCalledWith(mockFile);
    });

    it('should handle service errors gracefully', async () => {
      mockSyncStatusService.calculateSyncStatus.mockRejectedValue(new Error('Service error'));

      (view as any).currentFile = mockFile;
      await (view as any).updateSyncStatus();

      expect(mockSyncStatusService.calculateSyncStatus).toHaveBeenCalledWith(mockFile);
    });

    it('should skip update when no current file', async () => {
      (view as any).currentFile = null;
      await (view as any).updateSyncStatus();

      expect(mockSyncStatusService.calculateSyncStatus).not.toHaveBeenCalled();
    });
  });

  describe('View Properties', () => {
    it('should return correct view type', () => {
      expect(view.getViewType()).toBe('ghost-sync-status');
    });

    it('should return correct display text', () => {
      expect(view.getDisplayText()).toBe('Ghost');
    });

    it('should return correct icon', () => {
      expect(view.getIcon()).toBe('sync');
    });
  });

  describe('File Change Detection', () => {
    it('should update current file when active file changes', () => {
      const newFile = new TFile('new-article.md');
      mockPlugin.app.workspace.getActiveFile.mockReturnValue(newFile);

      (view as any).updateCurrentFile();

      expect((view as any).currentFile).toBe(newFile);
    });

    it('should not update when file has not changed', () => {
      (view as any).currentFile = mockFile;
      mockPlugin.app.workspace.getActiveFile.mockReturnValue(mockFile);

      const updateSyncStatusSpy = vi.spyOn(view as any, 'updateSyncStatus');

      (view as any).updateCurrentFile();

      expect(updateSyncStatusSpy).not.toHaveBeenCalled();
    });
  });
});
