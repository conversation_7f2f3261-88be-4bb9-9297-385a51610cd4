// Vitest setup file for Obsidian plugin testing
import { vi } from 'vitest';

// Mock crypto for JWT token generation
if (!global.crypto) {
  Object.defineProperty(global, 'crypto', {
    value: {
      subtle: {
        importKey: vi.fn().mockResolvedValue({}),
        sign: vi.fn().mockResolvedValue(new ArrayBuffer(32)),
      }
    }
  });
} else if (!global.crypto.subtle) {
  Object.defineProperty(global.crypto, 'subtle', {
    value: {
      importKey: vi.fn().mockResolvedValue({}),
      sign: vi.fn().mockResolvedValue(new ArrayBuffer(32)),
    }
  });
}

// Mock path module globally
vi.mock('path', () => ({
  normalize: vi.fn((path: string) => path),
  join: vi.fn((...paths: string[]) => paths.join('/')),
  dirname: vi.fn((path: string) => path.split('/').slice(0, -1).join('/')),
  basename: vi.fn((path: string) => path.split('/').pop() || ''),
}));

// Mock Obsidian APIs globally
vi.mock('obsidian', () => ({
  TFile: vi.fn(),
  Notice: vi.fn(),
  Plugin: vi.fn(),
  Setting: vi.fn(),
  PluginSettingTab: vi.fn(),
  ItemView: vi.fn(),
  WorkspaceLeaf: vi.fn(),
}));

// Mock TextEncoder/TextDecoder for JWT token generation
if (!global.TextEncoder) {
  const { TextEncoder, TextDecoder } = require('util');
  Object.defineProperty(global, 'TextEncoder', {
    value: TextEncoder
  });
  Object.defineProperty(global, 'TextDecoder', {
    value: TextDecoder
  });
}

// Mock btoa/atob for base64 encoding
if (!global.btoa) {
  Object.defineProperty(global, 'btoa', {
    value: (str: string) => Buffer.from(str).toString('base64')
  });
}

if (!global.atob) {
  Object.defineProperty(global, 'atob', {
    value: (str: string) => Buffer.from(str, 'base64').toString()
  });
}
