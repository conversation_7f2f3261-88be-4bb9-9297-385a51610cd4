import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SyncStatusCalculator } from '../../src/utils/sync-status';
import { createMockGhostPost, createMockSyncStatus } from '../test-utils';

// Mock the sync status calculator if it doesn't exist
const mockSyncStatusCalculator = {
  calculateSyncStatus: vi.fn(),
  compareValues: vi.fn(),
  getPropertyStatus: vi.fn()
};

describe('Sync Status Functionality', () => {
  let calculator: any;

  beforeEach(() => {
    vi.clearAllMocks();
    calculator = mockSyncStatusCalculator;
  });

  describe('Status Calculation', () => {
    it('should calculate synced status when values match', () => {
      const localPost = {
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        tags: ['test'],
        featured: false
      };

      const ghostPost = {
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        tags: [{ name: 'test' }],
        featured: false
      };

      calculator.calculateSyncStatus.mockReturnValue({
        title: 'synced',
        slug: 'synced',
        status: 'synced',
        tags: 'synced',
        featured: 'synced'
      });

      const result = calculator.calculateSyncStatus(localPost, ghostPost);

      expect(result.title).toBe('synced');
      expect(result.slug).toBe('synced');
      expect(result.status).toBe('synced');
    });

    it('should calculate different status when values differ', () => {
      const localPost = {
        title: 'Local Title',
        slug: 'local-slug',
        status: 'draft'
      };

      const ghostPost = {
        title: 'Ghost Title',
        slug: 'ghost-slug',
        status: 'published'
      };

      calculator.calculateSyncStatus.mockReturnValue({
        title: 'different',
        slug: 'different',
        status: 'different'
      });

      const result = calculator.calculateSyncStatus(localPost, ghostPost);

      expect(result.title).toBe('different');
      expect(result.slug).toBe('different');
      expect(result.status).toBe('different');
    });

    it('should calculate unknown status when ghost post is missing', () => {
      const localPost = {
        title: 'Local Title',
        slug: 'local-slug'
      };

      calculator.calculateSyncStatus.mockReturnValue({
        title: 'unknown',
        slug: 'unknown',
        status: 'unknown'
      });

      const result = calculator.calculateSyncStatus(localPost, null);

      expect(result.title).toBe('unknown');
      expect(result.slug).toBe('unknown');
      expect(result.status).toBe('unknown');
    });
  });

  describe('Value Comparison', () => {
    it('should compare simple string values', () => {
      calculator.compareValues.mockReturnValue('synced');
      
      const result = calculator.compareValues('same', 'same');
      expect(result).toBe('synced');

      calculator.compareValues.mockReturnValue('different');
      const result2 = calculator.compareValues('different1', 'different2');
      expect(result2).toBe('different');
    });

    it('should compare boolean values', () => {
      calculator.compareValues.mockReturnValue('synced');
      
      const result = calculator.compareValues(true, true);
      expect(result).toBe('synced');

      calculator.compareValues.mockReturnValue('different');
      const result2 = calculator.compareValues(true, false);
      expect(result2).toBe('different');
    });

    it('should compare array values', () => {
      calculator.compareValues.mockReturnValue('synced');
      
      const result = calculator.compareValues(['a', 'b'], ['a', 'b']);
      expect(result).toBe('synced');

      calculator.compareValues.mockReturnValue('different');
      const result2 = calculator.compareValues(['a', 'b'], ['a', 'c']);
      expect(result2).toBe('different');
    });

    it('should handle null and undefined values', () => {
      calculator.compareValues.mockReturnValue('unknown');
      
      const result1 = calculator.compareValues(null, undefined);
      const result2 = calculator.compareValues('value', null);
      const result3 = calculator.compareValues(undefined, 'value');

      expect(result1).toBe('unknown');
      expect(result2).toBe('unknown');
      expect(result3).toBe('unknown');
    });
  });

  describe('Property Status', () => {
    it('should get status for title property', () => {
      calculator.getPropertyStatus.mockReturnValue('synced');
      
      const result = calculator.getPropertyStatus('title', 'Same Title', 'Same Title');
      expect(result).toBe('synced');
    });

    it('should get status for tags property', () => {
      calculator.getPropertyStatus.mockReturnValue('different');
      
      const localTags = ['tag1', 'tag2'];
      const ghostTags = [{ name: 'tag1' }, { name: 'tag3' }];
      
      const result = calculator.getPropertyStatus('tags', localTags, ghostTags);
      expect(result).toBe('different');
    });

    it('should get status for featured property', () => {
      calculator.getPropertyStatus.mockReturnValue('different');
      
      const result = calculator.getPropertyStatus('featured', true, false);
      expect(result).toBe('different');
    });

    it('should get status for date properties', () => {
      calculator.getPropertyStatus.mockReturnValue('synced');
      
      const date1 = '2023-01-01T00:00:00.000Z';
      const date2 = '2023-01-01T00:00:00.000Z';
      
      const result = calculator.getPropertyStatus('published_at', date1, date2);
      expect(result).toBe('synced');
    });
  });

  describe('Complex Scenarios', () => {
    it('should handle posts with all properties synced', () => {
      const syncStatus = createMockSyncStatus({
        title: 'synced',
        slug: 'synced',
        status: 'synced',
        tags: 'synced',
        featured: 'synced',
        feature_image: 'synced',
        visibility: 'synced',
        primary_tag: 'synced'
      });

      expect(syncStatus.title).toBe('synced');
      expect(syncStatus.slug).toBe('synced');
      expect(syncStatus.status).toBe('synced');
      expect(syncStatus.tags).toBe('synced');
    });

    it('should handle posts with mixed sync status', () => {
      const syncStatus = createMockSyncStatus({
        title: 'synced',
        slug: 'different',
        status: 'synced',
        tags: 'different',
        featured: 'unknown'
      });

      expect(syncStatus.title).toBe('synced');
      expect(syncStatus.slug).toBe('different');
      expect(syncStatus.status).toBe('synced');
      expect(syncStatus.tags).toBe('different');
      expect(syncStatus.featured).toBe('unknown');
    });

    it('should handle posts with all properties unknown', () => {
      const syncStatus = createMockSyncStatus();

      expect(syncStatus.title).toBe('unknown');
      expect(syncStatus.slug).toBe('unknown');
      expect(syncStatus.status).toBe('unknown');
      expect(syncStatus.tags).toBe('unknown');
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty objects', () => {
      calculator.calculateSyncStatus.mockReturnValue(createMockSyncStatus());
      
      const result = calculator.calculateSyncStatus({}, {});
      expect(result).toBeTruthy();
    });

    it('should handle malformed data', () => {
      calculator.calculateSyncStatus.mockReturnValue(createMockSyncStatus());
      
      const result = calculator.calculateSyncStatus(null, undefined);
      expect(result).toBeTruthy();
    });

    it('should handle circular references', () => {
      const obj1: any = { title: 'test' };
      obj1.self = obj1;
      
      const obj2: any = { title: 'test' };
      obj2.self = obj2;

      calculator.calculateSyncStatus.mockReturnValue(createMockSyncStatus());
      
      expect(() => {
        calculator.calculateSyncStatus(obj1, obj2);
      }).not.toThrow();
    });
  });

  describe('Performance', () => {
    it('should handle large datasets efficiently', () => {
      const largePost = {
        title: 'Large Post',
        content: 'x'.repeat(10000),
        tags: Array.from({ length: 100 }, (_, i) => `tag${i}`)
      };

      calculator.calculateSyncStatus.mockReturnValue(createMockSyncStatus());
      
      const start = Date.now();
      calculator.calculateSyncStatus(largePost, largePost);
      const end = Date.now();

      // Should complete within reasonable time (adjust threshold as needed)
      expect(end - start).toBeLessThan(1000);
    });
  });
});
