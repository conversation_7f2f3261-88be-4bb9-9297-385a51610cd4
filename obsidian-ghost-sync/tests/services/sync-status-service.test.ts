import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SyncStatusService } from '../../src/services/sync-status-service';
import type { AppAdapter, FileMetadata } from '../../src/services/sync-status-service';
import type { ObsidianGhostAPI } from '../../src/api/ghost-api';
import type { GhostPost } from '../../src/types';
import { TFile } from 'obsidian';

describe('SyncStatusService', () => {
  let service: SyncStatusService;
  let mockGhostAPI: jest.Mocked<ObsidianGhostAPI>;
  let mockAppAdapter: jest.Mocked<AppAdapter>;
  let mockFile: TFile;

  beforeEach(() => {
    mockGhostAPI = {
      getPostBySlug: vi.fn()
    } as any;

    mockAppAdapter = {
      readFile: vi.fn(),
      getFileMetadata: vi.fn()
    };

    mockFile = new TFile('test-article.md');

    service = new SyncStatusService({
      ghostAPI: mockGhostAPI,
      appAdapter: mockAppAdapter
    });
  });

  describe('calculateSyncStatus', () => {
    it('should return unknown status when no frontmatter', async () => {
      mockAppAdapter.getFileMetadata.mockReturnValue(null);

      const result = await service.calculateSyncStatus(mockFile);

      expect(result.title).toBe('unknown');
      expect(result.slug).toBe('unknown');
      expect(result.status).toBe('unknown');
    });

    it('should return unknown status when no slug in frontmatter', async () => {
      const metadata: FileMetadata = {
        frontmatter: { title: 'Test Post' },
        content: 'Test content'
      };
      mockAppAdapter.getFileMetadata.mockReturnValue(metadata);

      const result = await service.calculateSyncStatus(mockFile);

      expect(result.title).toBe('unknown');
      expect(result.slug).toBe('unknown');
    });

    it('should return unknown status when Ghost post not found', async () => {
      const metadata: FileMetadata = {
        frontmatter: { slug: 'test-post' },
        content: 'Test content'
      };
      mockAppAdapter.getFileMetadata.mockReturnValue(metadata);
      mockGhostAPI.getPostBySlug.mockResolvedValue(null);

      const result = await service.calculateSyncStatus(mockFile);

      expect(result.title).toBe('unknown');
      expect(result.slug).toBe('unknown');
      expect(mockGhostAPI.getPostBySlug).toHaveBeenCalledWith('test-post');
    });

    it('should compare post data when Ghost post found', async () => {
      const metadata: FileMetadata = {
        frontmatter: {
          slug: 'test-post',
          title: 'Test Post',
          status: 'published',
          tags: ['tag1', 'tag2'],
          featured: false,
          visibility: 'public'
        },
        content: 'Test content'
      };

      const ghostPost: GhostPost = {
        id: '1',
        slug: 'test-post',
        title: 'Test Post',
        status: 'published',
        tags: [{ name: 'tag1' }, { name: 'tag2' }],
        featured: false,
        visibility: 'public',
        html: '<p>Test content</p>',
        created_at: '2023-01-01T00:00:00.000Z',
        updated_at: '2023-01-01T00:00:00.000Z',
        published_at: '2023-01-01T00:00:00.000Z'
      };

      mockAppAdapter.getFileMetadata.mockReturnValue(metadata);
      mockGhostAPI.getPostBySlug.mockResolvedValue(ghostPost);

      const result = await service.calculateSyncStatus(mockFile);

      expect(result.title).toBe('synced');
      expect(result.slug).toBe('synced');
      expect(result.status).toBe('synced');
      expect(result.tags).toBe('synced');
      expect(result.featured).toBe('synced');
      expect(result.visibility).toBe('synced');
      expect(result.ghostPost).toBe(ghostPost);
    });

    it('should detect differences in post data', async () => {
      const metadata: FileMetadata = {
        frontmatter: {
          slug: 'test-post',
          title: 'Different Title',
          status: 'draft',
          tags: ['tag1'],
          featured: true
        },
        content: 'Test content'
      };

      const ghostPost: GhostPost = {
        id: '1',
        slug: 'test-post',
        title: 'Test Post',
        status: 'published',
        tags: [{ name: 'tag1' }, { name: 'tag2' }],
        featured: false,
        html: '<p>Test content</p>',
        created_at: '2023-01-01T00:00:00.000Z',
        updated_at: '2023-01-01T00:00:00.000Z',
        published_at: '2023-01-01T00:00:00.000Z'
      };

      mockAppAdapter.getFileMetadata.mockReturnValue(metadata);
      mockGhostAPI.getPostBySlug.mockResolvedValue(ghostPost);

      const result = await service.calculateSyncStatus(mockFile);

      expect(result.title).toBe('different');
      expect(result.slug).toBe('synced');
      expect(result.status).toBe('different');
      expect(result.tags).toBe('different');
      expect(result.featured).toBe('different');
    });

    it('should handle capitalized frontmatter properties', async () => {
      const metadata: FileMetadata = {
        frontmatter: {
          Slug: 'test-post',
          Title: 'Test Post',
          Status: 'published'
        },
        content: 'Test content'
      };

      const ghostPost: GhostPost = {
        id: '1',
        slug: 'test-post',
        title: 'Test Post',
        status: 'published',
        html: '<p>Test content</p>',
        created_at: '2023-01-01T00:00:00.000Z',
        updated_at: '2023-01-01T00:00:00.000Z',
        published_at: '2023-01-01T00:00:00.000Z'
      };

      mockAppAdapter.getFileMetadata.mockReturnValue(metadata);
      mockGhostAPI.getPostBySlug.mockResolvedValue(ghostPost);

      const result = await service.calculateSyncStatus(mockFile);

      expect(result.title).toBe('synced');
      expect(result.slug).toBe('synced');
      expect(result.status).toBe('synced');
    });

    it('should handle API errors gracefully', async () => {
      const metadata: FileMetadata = {
        frontmatter: { slug: 'test-post' },
        content: 'Test content'
      };
      mockAppAdapter.getFileMetadata.mockReturnValue(metadata);
      mockGhostAPI.getPostBySlug.mockRejectedValue(new Error('API Error'));

      const result = await service.calculateSyncStatus(mockFile);

      expect(result.title).toBe('unknown');
      expect(result.slug).toBe('unknown');
    });
  });

  describe('isValidForSync', () => {
    it('should return false for undefined frontmatter', () => {
      expect(service.isValidForSync(undefined)).toBe(false);
    });

    it('should return false for frontmatter without slug', () => {
      expect(service.isValidForSync({ title: 'Test' })).toBe(false);
    });

    it('should return false for empty slug', () => {
      expect(service.isValidForSync({ slug: '' })).toBe(false);
      expect(service.isValidForSync({ slug: '   ' })).toBe(false);
    });

    it('should return true for valid slug', () => {
      expect(service.isValidForSync({ slug: 'test-post' })).toBe(true);
      expect(service.isValidForSync({ Slug: 'test-post' })).toBe(true);
    });
  });

  describe('extractFrontmatterValues', () => {
    it('should extract all frontmatter values', () => {
      const frontmatter = {
        title: 'Test Post',
        slug: 'test-post',
        status: 'published',
        tags: ['tag1', 'tag2'],
        featured: false,
        visibility: 'public'
      };

      const result = service.extractFrontmatterValues(frontmatter);

      expect(result.title).toBe('Test Post');
      expect(result.slug).toBe('test-post');
      expect(result.status).toBe('published');
      expect(result.tags).toEqual(['tag1', 'tag2']);
      expect(result.featured).toBe(false);
      expect(result.visibility).toBe('public');
    });

    it('should handle capitalized property names', () => {
      const frontmatter = {
        Title: 'Test Post',
        Slug: 'test-post',
        Status: 'published'
      };

      const result = service.extractFrontmatterValues(frontmatter);

      expect(result.title).toBe('Test Post');
      expect(result.slug).toBe('test-post');
      expect(result.status).toBe('published');
    });
  });

  describe('tag comparison', () => {
    it('should handle empty tags correctly', async () => {
      const metadata: FileMetadata = {
        frontmatter: {
          slug: 'test-post',
          tags: []
        },
        content: 'Test content'
      };

      const ghostPost: GhostPost = {
        id: '1',
        slug: 'test-post',
        title: 'Test Post',
        tags: [],
        html: '<p>Test content</p>',
        created_at: '2023-01-01T00:00:00.000Z',
        updated_at: '2023-01-01T00:00:00.000Z',
        published_at: '2023-01-01T00:00:00.000Z'
      };

      mockAppAdapter.getFileMetadata.mockReturnValue(metadata);
      mockGhostAPI.getPostBySlug.mockResolvedValue(ghostPost);

      const result = await service.calculateSyncStatus(mockFile);

      expect(result.tags).toBe('synced');
    });

    it('should handle tag order differences', async () => {
      const metadata: FileMetadata = {
        frontmatter: {
          slug: 'test-post',
          tags: ['tag2', 'tag1']
        },
        content: 'Test content'
      };

      const ghostPost: GhostPost = {
        id: '1',
        slug: 'test-post',
        title: 'Test Post',
        tags: [{ name: 'tag1' }, { name: 'tag2' }],
        html: '<p>Test content</p>',
        created_at: '2023-01-01T00:00:00.000Z',
        updated_at: '2023-01-01T00:00:00.000Z',
        published_at: '2023-01-01T00:00:00.000Z'
      };

      mockAppAdapter.getFileMetadata.mockReturnValue(metadata);
      mockGhostAPI.getPostBySlug.mockResolvedValue(ghostPost);

      const result = await service.calculateSyncStatus(mockFile);

      expect(result.tags).toBe('synced');
    });
  });
});
