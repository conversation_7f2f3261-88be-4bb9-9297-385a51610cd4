import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ObsidianAppAdapter } from '../../src/services/obsidian-app-adapter';
import { App, TFile } from 'obsidian';

describe('ObsidianAppAdapter', () => {
  let adapter: ObsidianAppAdapter;
  let mockApp: jest.Mocked<App>;
  let mockFile: TFile;

  beforeEach(() => {
    mockApp = {
      vault: {
        read: vi.fn()
      },
      metadataCache: {
        getFileCache: vi.fn()
      }
    } as any;

    mockFile = new TFile('test-article.md');
    adapter = new ObsidianAppAdapter(mockApp);
  });

  describe('readFile', () => {
    it('should read file content', async () => {
      const content = 'Test file content';
      mockApp.vault.read.mockResolvedValue(content);

      const result = await adapter.readFile(mockFile);

      expect(result).toBe(content);
      expect(mockApp.vault.read).toHaveBeenCalledWith(mockFile);
    });

    it('should handle read errors', async () => {
      const error = new Error('File not found');
      mockApp.vault.read.mockRejectedValue(error);

      await expect(adapter.readFile(mockFile)).rejects.toThrow('File not found');
    });
  });

  describe('getFileMetadata', () => {
    it('should return metadata when cache exists', () => {
      const mockCache = {
        frontmatter: {
          title: 'Test Post',
          slug: 'test-post'
        }
      };
      mockApp.metadataCache.getFileCache.mockReturnValue(mockCache);

      const result = adapter.getFileMetadata(mockFile);

      expect(result).toEqual({
        frontmatter: mockCache.frontmatter,
        content: ''
      });
      expect(mockApp.metadataCache.getFileCache).toHaveBeenCalledWith(mockFile);
    });

    it('should return null when no cache exists', () => {
      mockApp.metadataCache.getFileCache.mockReturnValue(null);

      const result = adapter.getFileMetadata(mockFile);

      expect(result).toEqual({
        frontmatter: undefined,
        content: ''
      });
    });

    it('should handle cache errors gracefully', () => {
      mockApp.metadataCache.getFileCache.mockImplementation(() => {
        throw new Error('Cache error');
      });

      const result = adapter.getFileMetadata(mockFile);

      expect(result).toBeNull();
    });
  });

  describe('getFileMetadataWithRetry', () => {
    it('should return metadata on first try when available', async () => {
      const mockCache = {
        frontmatter: {
          title: 'Test Post',
          slug: 'test-post'
        }
      };
      mockApp.metadataCache.getFileCache.mockReturnValue(mockCache);

      const result = await adapter.getFileMetadataWithRetry(mockFile);

      expect(result).toEqual({
        frontmatter: mockCache.frontmatter,
        content: ''
      });
      expect(mockApp.metadataCache.getFileCache).toHaveBeenCalledTimes(1);
    });

    it('should retry when frontmatter is initially undefined', async () => {
      const mockCache = {
        frontmatter: {
          title: 'Test Post',
          slug: 'test-post'
        }
      };

      // First call returns no frontmatter, second call returns frontmatter
      mockApp.metadataCache.getFileCache
        .mockReturnValueOnce({ frontmatter: undefined })
        .mockReturnValueOnce(mockCache);

      const result = await adapter.getFileMetadataWithRetry(mockFile, 2, 10);

      expect(result).toEqual({
        frontmatter: mockCache.frontmatter,
        content: ''
      });
      expect(mockApp.metadataCache.getFileCache).toHaveBeenCalledTimes(2);
    });

    it('should return final result after max retries', async () => {
      // Always return no frontmatter
      mockApp.metadataCache.getFileCache.mockReturnValue({ frontmatter: undefined });

      const result = await adapter.getFileMetadataWithRetry(mockFile, 2, 10);

      expect(result).toEqual({
        frontmatter: undefined,
        content: ''
      });
      expect(mockApp.metadataCache.getFileCache).toHaveBeenCalledTimes(3); // 2 retries + 1 final call
    });

    it('should use default retry parameters', async () => {
      const mockCache = {
        frontmatter: {
          title: 'Test Post',
          slug: 'test-post'
        }
      };
      mockApp.metadataCache.getFileCache.mockReturnValue(mockCache);

      const result = await adapter.getFileMetadataWithRetry(mockFile);

      expect(result).toEqual({
        frontmatter: mockCache.frontmatter,
        content: ''
      });
    });
  });
});
