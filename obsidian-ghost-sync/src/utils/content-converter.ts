// Import HTML to Markdown converter
const TurndownService = require('turndown');
import { PropertyMapper } from './property-mapping';

// Content conversion utilities
export class ContentConverter {
  static htmlToMarkdown(html: string): string {
    if (!html) return '';

    // Use Turndown for proper HTML to Markdown conversion
    const turndownService = new TurndownService({
      headingStyle: 'atx',
      codeBlockStyle: 'fenced',
      fence: '```',
      emDelimiter: '*',
      strongDelimiter: '**',
      linkStyle: 'inlined',
      linkReferenceStyle: 'full'
    });

    // Add custom rule for code blocks with language detection
    turndownService.addRule('codeBlock', {
      filter: function (node: any) {
        return node.nodeName === 'PRE' && node.firstChild && node.firstChild.nodeName === 'CODE';
      },
      replacement: function (content: string, node: any) {
        const codeElement = node.firstChild;
        const className = codeElement.className || '';
        const languageMatch = className.match(/language-(\w+)/);
        const language = languageMatch ? languageMatch[1] : '';

        // Clean up the content by removing extra whitespace
        const cleanContent = content.trim();

        return '\n\n```' + language + '\n' + cleanContent + '\n```\n\n';
      }
    });

    // Add custom rule for Ghost callout cards to convert back to Obsidian callouts
    turndownService.addRule('calloutCard', {
      filter: function (node: any) {
        return node.nodeName === 'DIV' &&
               node.classList &&
               node.classList.contains('kg-callout-card');
      },
      replacement: function (content: string, node: any) {
        // Extract emoji and text from the callout card
        const emojiElement = node.querySelector('.kg-callout-emoji');
        const textElement = node.querySelector('.kg-callout-text');

        if (!textElement) return content;

        const emoji = emojiElement ? emojiElement.textContent : '';
        const text = textElement.textContent || '';

        // Map emoji back to callout type
        const calloutType = ContentConverter.getCalloutTypeFromEmoji(emoji);

        return `\n\n> [!${calloutType}]\n> ${text}\n\n`;
      }
    });

    return turndownService.turndown(html);
  }

  static createFilename(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  static slugify(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  static normalizeFrontMatter(frontMatter: any): any {
    return PropertyMapper.normalizeToGhost(frontMatter);
  }

  static createGhostPostData(frontMatter: any, markdownContent: string, options: any = {}): any {
    const { status = 'draft', isUpdate = false, existingPost = null } = options;

    // Map title-cased properties to lowercase for backward compatibility
    const normalizedFrontMatter = this.normalizeFrontMatter(frontMatter);

    const slug = normalizedFrontMatter.slug || this.slugify(normalizedFrontMatter.title);
    const newStatus = normalizedFrontMatter.status || status;

    const postData: any = {
      title: normalizedFrontMatter.title,
      slug: slug,
      feature_image: normalizedFrontMatter.feature_image || normalizedFrontMatter.image || null,
      featured: normalizedFrontMatter.featured || false,
      status: newStatus,
      visibility: normalizedFrontMatter.visibility || 'public',
      custom_excerpt: this.generateExcerpt(normalizedFrontMatter, markdownContent)
    };

    // Handle tags with primary tag support
    if (normalizedFrontMatter.tags && Array.isArray(normalizedFrontMatter.tags)) {
      let tags = [...normalizedFrontMatter.tags];

      // If primary tag is specified, ensure it's first in the array
      if (normalizedFrontMatter.primary_tag && typeof normalizedFrontMatter.primary_tag === 'string') {
        // Remove primary tag from current position if it exists
        tags = tags.filter(tag => tag !== normalizedFrontMatter.primary_tag);
        // Add primary tag at the beginning
        tags.unshift(normalizedFrontMatter.primary_tag);
      }

      postData.tags = tags;
    }

    // Handle newsletter
    if (normalizedFrontMatter.newsletter && typeof normalizedFrontMatter.newsletter === 'string') {
      // Note: We store the newsletter name in frontmatter, but Ghost API expects newsletter ID
      // The actual newsletter ID resolution will be handled in the API layer
      postData.newsletter_name = normalizedFrontMatter.newsletter;
    }

    // Handle published_at date carefully
    if (isUpdate && existingPost) {
      // CRITICAL: Include the post ID and updated_at for updates
      postData.id = existingPost.id;
      postData.updated_at = existingPost.updated_at;

      // When updating an existing post
      const existingStatus = existingPost.status;
      const existingPublishedAt = existingPost.published_at;

      if (existingStatus === 'draft' && newStatus === 'published') {
        // Draft → Published transition: Set published_at to now
        postData.published_at = new Date().toISOString();
      } else if (existingPublishedAt) {
        // Post was already published: Preserve existing published_at date
        postData.published_at = existingPublishedAt;
      } else if (newStatus === 'published') {
        // Edge case: Post has no published_at but status is published
        postData.published_at = new Date().toISOString();
      }
      // For draft posts, don't set published_at (Ghost will handle it)
    } else {
      // Creating new post
      if (newStatus === 'published') {
        // New published post: Use frontmatter date or current time
        const postDate = this.parseDate(normalizedFrontMatter.published_at) ||
                        this.parseDate(normalizedFrontMatter.date) ||
                        new Date();
        postData.published_at = postDate.toISOString();
      }
      // For new draft posts, don't set published_at
    }

    // CRITICAL FIX: Generate HTML content and handle lexical properly
    const htmlContent = this.markdownToHtml(markdownContent);

    // Ensure we never send empty HTML content
    if (!htmlContent || htmlContent.trim() === '' || htmlContent.trim() === '<p></p>') {
      // Provide minimal HTML content to prevent clearing
      postData.html = '<p>Content is being updated...</p>';
    } else {
      postData.html = htmlContent;
    }

    // CRITICAL FIX: Handle lexical content properly
    if (isUpdate) {
      // When updating, DO NOT set lexical to null - this preserves existing lexical content
      // Only set HTML which Ghost can convert to lexical if needed
      // Do not include lexical field at all to preserve existing content
    } else {
      // For new posts, we can set lexical to null since there's no existing content
      postData.lexical = null;
    }

    // Always set mobiledoc to null (deprecated format)
    postData.mobiledoc = null;

    return postData;
  }

  static parseDate(dateStr: string): Date | null {
    if (!dateStr) return null;
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? null : date;
  }

  static generateExcerpt(frontMatter: any, content: string): string | null {
    // Always generate excerpt from content (no frontmatter excerpt support)
    if (content) {
      // Get plain text from markdown
      const plaintext = content.replace(/[#*`_\[\]()]/g, '').trim();

      if (plaintext.length <= 300) {
        return plaintext;
      }

      // Truncate at word boundary
      const truncated = plaintext.substring(0, 297);
      const lastSpace = truncated.lastIndexOf(' ');

      if (lastSpace > 250) {
        return truncated.substring(0, lastSpace) + '...';
      }

      return truncated + '...';
    }

    return null;
  }

  static markdownToHtml(markdown: string): string {
    // Handle empty or whitespace-only content
    if (!markdown || markdown.trim() === '') {
      return '<p></p>';
    }

    // Use a proper markdown parser to avoid content loss
    // This is a basic but safe implementation that preserves content structure

    // First, handle code blocks to protect them from other transformations
    const codeBlocks: string[] = [];
    let html = markdown.replace(/```(\w+)?\n([\s\S]*?)```/g, (_match, lang, code) => {
      const placeholder = `__CODE_BLOCK_${codeBlocks.length}__`;
      const langClass = lang ? ` class="language-${lang}"` : '';
      codeBlocks.push(`<pre><code${langClass}>${this.escapeHtml(code.trim())}</code></pre>`);
      return placeholder;
    });

    // Handle Obsidian callouts - convert to Ghost callout cards
    html = this.convertObsidianCallouts(html);

    // Handle inline code
    html = html.replace(/`([^`]+)`/g, '<code>$1</code>');

    // Handle headers
    html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>');
    html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>');
    html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>');

    // Handle bold and italic
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // Handle links
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');

    // Handle line breaks (convert double newlines to paragraphs, single to br)
    html = html.replace(/\n\n/g, '</p><p>');
    html = html.replace(/\n/g, '<br>');

    // Wrap in paragraphs if not empty
    if (html.trim()) {
      html = `<p>${html}</p>`;
    } else {
      html = '<p></p>';
    }

    // Clean up empty paragraphs
    html = html.replace(/<p><\/p>/g, '');
    html = html.replace(/<p><br><\/p>/g, '');

    // Restore code blocks
    codeBlocks.forEach((block, index) => {
      html = html.replace(`__CODE_BLOCK_${index}__`, block);
    });

    // Ensure we always return valid HTML
    return html || '<p></p>';
  }

  static escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * Convert Obsidian callouts to Ghost callout cards
   */
  static convertObsidianCallouts(markdown: string): string {
    // Regex to match Obsidian callout syntax: > [!type] followed by content lines starting with >
    const calloutRegex = /^> \[!(\w+)\]\s*\n((?:> .*(?:\n|$))*)/gm;

    return markdown.replace(calloutRegex, (match, type, content) => {
      // Extract the content and remove the leading "> " from each line
      const calloutText = content
        .split('\n')
        .map((line: string) => line.replace(/^> ?/, ''))
        .filter((line: string) => line.trim() !== '')
        .join(' ')
        .trim();

      // Get emoji and CSS class for the callout type
      const { emoji, cssClass } = this.getCalloutTypeInfo(type.toLowerCase());

      // Generate Ghost callout card HTML
      const placeholder = `__CALLOUT_${Math.random().toString(36).substr(2, 9)}__`;
      const calloutHtml = `<div class="kg-card kg-callout-card ${cssClass}">
    <div class="kg-callout-emoji">${emoji}</div>
    <div class="kg-callout-text">${calloutText}</div>
</div>`;

      // Store the callout HTML for later restoration (similar to code blocks)
      // For now, return the HTML directly since we're not using placeholders for callouts
      return calloutHtml;
    });
  }

  /**
   * Get emoji and CSS class for callout type
   */
  static getCalloutTypeInfo(type: string): { emoji: string; cssClass: string } {
    const calloutTypes: Record<string, { emoji: string; cssClass: string }> = {
      note: { emoji: '📝', cssClass: 'kg-callout-card-blue' },
      info: { emoji: 'ℹ️', cssClass: 'kg-callout-card-blue' },
      tip: { emoji: '💡', cssClass: 'kg-callout-card-green' },
      success: { emoji: '✅', cssClass: 'kg-callout-card-green' },
      warning: { emoji: '⚠️', cssClass: 'kg-callout-card-yellow' },
      caution: { emoji: '⚠️', cssClass: 'kg-callout-card-yellow' },
      danger: { emoji: '❌', cssClass: 'kg-callout-card-red' },
      error: { emoji: '🚨', cssClass: 'kg-callout-card-red' },
      question: { emoji: '❓', cssClass: 'kg-callout-card-blue' },
      quote: { emoji: '💬', cssClass: 'kg-callout-card-accent' },
      example: { emoji: '📋', cssClass: 'kg-callout-card-blue' },
      abstract: { emoji: '📄', cssClass: 'kg-callout-card-blue' },
      summary: { emoji: '📄', cssClass: 'kg-callout-card-blue' },
      todo: { emoji: '☑️', cssClass: 'kg-callout-card-blue' },
      bug: { emoji: '🐛', cssClass: 'kg-callout-card-red' },
      failure: { emoji: '❌', cssClass: 'kg-callout-card-red' },
      fail: { emoji: '❌', cssClass: 'kg-callout-card-red' },
      missing: { emoji: '❓', cssClass: 'kg-callout-card-red' }
    };

    // Return the mapping for the type, or default to note if not found
    return calloutTypes[type] || calloutTypes.note;
  }

  /**
   * Get callout type from emoji (for reverse conversion)
   */
  static getCalloutTypeFromEmoji(emoji: string): string {
    const emojiToType: Record<string, string> = {
      '📝': 'note',
      'ℹ️': 'info',
      '💡': 'tip',
      '✅': 'success',
      '⚠️': 'warning',
      '❌': 'danger',
      '🚨': 'error',
      '❓': 'question',
      '💬': 'quote',
      '📋': 'example',
      '📄': 'abstract',
      '☑️': 'todo',
      '🐛': 'bug'
    };

    return emojiToType[emoji] || 'note';
  }

  static parseArticle(content: string): { frontMatter: any, markdownContent: string } {
    const frontMatterMatch = content.match(/^---\n([\s\S]*?)\n---\n([\s\S]*)$/);
    if (!frontMatterMatch) {
      throw new Error('No front matter found in article');
    }

    const frontMatter = this.parseYaml(frontMatterMatch[1]);
    const markdownContent = frontMatterMatch[2].trim();

    return { frontMatter, markdownContent };
  }

  static parseYaml(yamlString: string): any {
    const result: any = {};
    const lines = yamlString.split('\n');
    let currentKey: string | null = null;
    let currentArray: string[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmed = line.trim();

      if (!trimmed || trimmed.startsWith('#')) continue;

      // Handle array items (lines starting with "- ")
      if (trimmed.startsWith('- ')) {
        if (currentKey) {
          currentArray.push(trimmed.substring(2).trim());
        }
        continue;
      }

      // If we were building an array, save it now
      if (currentKey && currentArray.length > 0) {
        result[currentKey] = currentArray;
        currentKey = null;
        currentArray = [];
      }

      // Handle key-value pairs
      if (trimmed.includes(':')) {
        const [key, ...valueParts] = trimmed.split(':');
        const value = valueParts.join(':').trim();
        const cleanKey = key.trim();

        if (value === '' || value === null) {
          // This might be the start of an array
          currentKey = cleanKey;
          currentArray = [];
        } else if (value.startsWith('"') && value.endsWith('"')) {
          result[cleanKey] = value.slice(1, -1);
        } else if (value.startsWith('[') && value.endsWith(']')) {
          result[cleanKey] = value.slice(1, -1).split(',').map(s => s.trim());
        } else if (value === 'true') {
          result[cleanKey] = true;
        } else if (value === 'false') {
          result[cleanKey] = false;
        } else if (value === 'null') {
          result[cleanKey] = null;
        } else if (!isNaN(Number(value))) {
          result[cleanKey] = Number(value);
        } else {
          result[cleanKey] = value;
        }
      }
    }

    // Handle any remaining array
    if (currentKey && currentArray.length > 0) {
      result[currentKey] = currentArray;
    }

    return result;
  }

  static objectToYaml(obj: any): string {
    let yaml = '';
    for (const [key, value] of Object.entries(obj)) {
      if (Array.isArray(value)) {
        yaml += `${key}:\n`;
        for (const item of value) {
          yaml += `  - ${item}\n`;
        }
      } else if (typeof value === 'string') {
        yaml += `${key}: "${value}"\n`;
      } else {
        yaml += `${key}: ${value}\n`;
      }
    }
    return yaml;
  }

  static convertGhostPostToArticle(post: any): string {
    const tags = post.tags ? post.tags.map((tag: any) => tag.name) : [];
    const createdDate = post.created_at ? new Date(post.created_at) : new Date();
    const updatedDate = post.updated_at ? new Date(post.updated_at) : new Date();
    const publishedDate = post.published_at ? new Date(post.published_at) : null;

    // Create frontmatter using the centralized property mapping
    const frontmatter = PropertyMapper.normalizeToObsidian({
      title: post.title,
      slug: post.slug,
      status: post.status || 'draft',
      tags: tags,
      primary_tag: post.primary_tag?.name || null,
      visibility: post.visibility || 'public',
      feature_image: post.feature_image || null,
      featured: post.featured || false,
      newsletter: post.newsletter?.name || null,
      email_sent: post.email ? 'Yes' : 'No',
      created_at: post.created_at,
      updated_at: post.updated_at,
      published_at: post.published_at
    });

    let content = '';

    // MODERN GHOST → LOCAL CONVERSION: Extract markdown from lexical OR convert HTML
    if (post.lexical) {
      try {
        console.log(`✅ LEXICAL PROCESSING: Extracting content for "${post.title}"`);
        const lexicalDoc = JSON.parse(post.lexical);

        // Look for markdown cards in lexical document
        const markdownCard = this.extractMarkdownFromLexical(lexicalDoc);
        if (markdownCard) {
          console.log(`✅ MARKDOWN EXTRACTED: Found markdown card in lexical`);
          content = markdownCard;
        } else {
          console.log(`⚠️ NO MARKDOWN CARD: Converting HTML to markdown`);
          content = this.htmlToMarkdown(post.html);
        }
      } catch (error) {
        console.warn(`⚠️ LEXICAL ERROR: Failed to process lexical for "${post.title}", using HTML`);
        content = this.htmlToMarkdown(post.html);
      }
    } else if (post.html) {
      // Direct HTML → Markdown conversion
      console.log(`✅ HTML → Markdown conversion for "${post.title}"`);
      content = this.htmlToMarkdown(post.html);
    } else {
      console.error(`❌ NO CONTENT: Post "${post.title}" has no lexical or HTML content`);
      content = '';
    }

    const yamlFrontmatter = this.objectToYaml(frontmatter);
    return `---\n${yamlFrontmatter}---\n\n${content}`;
  }

  static extractMarkdownFromLexical(lexicalDoc: any): string | null {
    // Extract markdown content from lexical document structure
    try {
      if (lexicalDoc?.root?.children) {
        for (const child of lexicalDoc.root.children) {
          // Look for markdown cards
          if (child.type === 'markdown' && child.markdown) {
            return child.markdown;
          }
        }
      }
      return null;
    } catch (error) {
      console.warn('Failed to extract markdown from lexical:', error);
      return null;
    }
  }
}
