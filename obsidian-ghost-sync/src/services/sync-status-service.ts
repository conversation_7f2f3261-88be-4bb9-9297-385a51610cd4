import type { TFile } from "obsidian";
import type { GhostPost } from "../types";
import type { SyncStatusData } from "../components/types";
import type { ObsidianGhostAPI } from "../api/ghost-api";
import { PropertyMapper, PROPERTY_MAPPINGS } from "../utils/property-mapping";

export interface FileMetadata {
  frontmatter?: Record<string, any>;
  content: string;
}

export interface AppAdapter {
  readFile(file: TFile): Promise<string>;
  getFileMetadata(file: TFile): FileMetadata | null;
}

export interface SyncStatusServiceDependencies {
  ghostAPI: ObsidianGhostAPI;
  appAdapter: AppAdapter;
}

export class SyncStatusService {
  private ghostAPI: ObsidianGhostAPI;
  private appAdapter: AppAdapter;

  constructor(dependencies: SyncStatusServiceDependencies) {
    this.ghostAPI = dependencies.ghostAPI;
    this.appAdapter = dependencies.appAdapter;
  }

  async calculateSyncStatus(file: TFile): Promise<SyncStatusData> {
    try {
      const metadata = this.appAdapter.getFileMetadata(file);

      if (!metadata?.frontmatter) {
        return this.createUnknownStatus();
      }

      const slug = this.extractSlug(metadata.frontmatter);

      if (!slug) {
        return this.createUnknownStatus();
      }

      const ghostPost = await this.ghostAPI.getPostBySlug(slug);

      if (!ghostPost) {
        return this.createUnknownStatus();
      }

      return this.comparePostData(metadata.frontmatter, ghostPost);
    } catch (error) {
      console.error('Error calculating sync status:', error);
      return this.createUnknownStatus();
    }
  }

  private extractSlug(frontmatter: Record<string, any>): string | null {
    // Handle both lowercase and capitalized property names
    return frontmatter.slug || frontmatter.Slug || null;
  }

  private createUnknownStatus(): SyncStatusData {
    const status: any = {};

    // Create unknown status for all display properties
    for (const mapping of PropertyMapper.getDisplayProperties()) {
      status[mapping.ghostProperty] = 'unknown';
    }

    return status as SyncStatusData;
  }

  private comparePostData(frontmatter: Record<string, any>, ghostPost: GhostPost): SyncStatusData {
    const status: any = {};

    // Compare all display properties using the centralized mapping
    for (const mapping of PropertyMapper.getDisplayProperties()) {
      status[mapping.ghostProperty] = PropertyMapper.compareValues(ghostPost, frontmatter, mapping);
    }

    // Add the Ghost post for reference
    status.ghostPost = ghostPost;

    return status as SyncStatusData;
  }



  /**
   * Validates if a frontmatter object contains the minimum required fields for sync
   */
  isValidForSync(frontmatter: Record<string, any> | undefined): boolean {
    if (!frontmatter) return false;
    const slug = this.extractSlug(frontmatter);
    return !!slug && slug.trim().length > 0;
  }

  /**
   * Extracts all relevant frontmatter values in a normalized format using property mapping
   */
  extractFrontmatterValues(frontmatter: Record<string, any>): Record<string, any> {
    return PropertyMapper.normalizeToGhost(frontmatter);
  }

  /**
   * Normalizes frontmatter to use canonical Obsidian property names
   */
  normalizeFrontmatter(frontmatter: Record<string, any>): Record<string, any> {
    return PropertyMapper.normalizeToObsidian(frontmatter);
  }
}
