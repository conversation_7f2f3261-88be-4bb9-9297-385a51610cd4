<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { TFile } from 'obsidian';
  import PropertyDisplay from './PropertyDisplay.svelte';
  import type { SyncStatusData } from './types';
  import * as path from 'path';

  export let currentFile: TFile | null = null;
  export let syncStatus: SyncStatusData = {
    title: 'unknown',
    slug: 'unknown',
    status: 'unknown',
    tags: 'unknown',
    featured: 'unknown',
    feature_image: 'unknown',
    visibility: 'unknown',
    primary_tag: 'unknown',
    created_at: 'unknown',
    updated_at: 'unknown',
    published_at: 'unknown',
    newsletter: 'unknown',
    email_sent: 'unknown'
  };
  export let plugin: any = null;

  const dispatch = createEventDispatcher();

  let isInArticlesDir = false;
  let articlesPath = '';

  $: {
    if (currentFile && plugin?.settings?.articlesDir) {
      articlesPath = path.normalize(plugin.settings.articlesDir);
      const filePath = path.normalize(currentFile.path);
      isInArticlesDir = filePath.startsWith(articlesPath);
    } else {
      isInArticlesDir = false;
    }
  }

  async function handleSmartSync() {
    dispatch('smartSync');
  }

  function handlePublish() {
    dispatch('publish');
  }

  function handleBrowsePosts() {
    if (plugin && plugin.browseGhostPosts) {
      plugin.browseGhostPosts();
    } else {
      dispatch('browsePosts');
    }
  }
</script>

<div class="ghost-sync-status-view">
  <!-- Header -->
  <div class="ghost-sync-header">
    <h3>Ghost</h3>
  </div>

  {#if !currentFile}
    <p class="ghost-sync-no-file">No file selected</p>
  {:else if !isInArticlesDir}
    <p class="ghost-sync-not-article">
      File must be in {plugin.settings.articlesDir} directory
    </p>
  {:else}
    <!-- Feature Image Preview -->
    {#if syncStatus.ghostPost?.feature_image}
      <div class="ghost-sync-feature-image-container">
        <img
          src={syncStatus.ghostPost.feature_image}
          alt="Feature image"
          class="ghost-sync-feature-image"
        />
      </div>
    {/if}

    <!-- Status List -->
    <div class="ghost-sync-status-list">
      <PropertyDisplay
        label="Title"
        status={syncStatus.title}
        value={syncStatus.ghostPost?.title}
      />
      <PropertyDisplay
        label="Slug"
        status={syncStatus.slug}
        value={syncStatus.ghostPost?.slug}
      />
      <PropertyDisplay
        label="Status"
        status={syncStatus.status}
        value={syncStatus.ghostPost?.status}
      />
      <PropertyDisplay
        label="Tags"
        status={syncStatus.tags}
        value={syncStatus.ghostPost?.tags}
        isTagsList={true}
      />
      <PropertyDisplay
        label="Primary Tag"
        status={syncStatus.primary_tag}
        value={syncStatus.ghostPost?.primary_tag?.name}
      />
      <PropertyDisplay
        label="Visibility"
        status={syncStatus.visibility}
        value={syncStatus.ghostPost?.visibility}
      />
      <PropertyDisplay
        label="Featured"
        status={syncStatus.featured}
        value={syncStatus.ghostPost?.featured}
      />
      <PropertyDisplay
        label="Feature Image"
        status={syncStatus.feature_image}
        value={syncStatus.ghostPost?.feature_image ? 'Set' : 'None'}
      />
      <PropertyDisplay
        label="Newsletter"
        status={syncStatus.newsletter}
        value={syncStatus.ghostPost?.newsletter?.name || 'None'}
      />
      <PropertyDisplay
        label="Email Sent"
        status={syncStatus.email_sent}
        value={syncStatus.ghostPost?.email ? 'Yes' : 'No'}
      />
    </div>

    <!-- Buttons -->
    <div class="ghost-sync-buttons">
      <button
        class="ghost-sync-btn"
        on:click={handleSmartSync}
      >
        Sync
      </button>

      {#if syncStatus.ghostPost}
        <button
          class="ghost-sync-btn mod-cta"
          class:disabled={syncStatus.ghostPost.status === 'published' && !!syncStatus.ghostPost.email}
          disabled={syncStatus.ghostPost.status === 'published' && !!syncStatus.ghostPost.email}
          on:click={handlePublish}
        >
          Publish
        </button>
      {/if}

      <button
        class="ghost-sync-btn"
        on:click={handleBrowsePosts}
      >
        Browse Posts
      </button>


    </div>


  {/if}
</div>

<style>
  .ghost-sync-feature-image-container {
    margin-bottom: 16px;
  }

  .ghost-sync-feature-image {
    width: 100%;
    max-height: 200px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid var(--background-modifier-border);
  }

  .ghost-sync-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .ghost-sync-btn.disabled:hover {
    opacity: 0.5;
  }
</style>
