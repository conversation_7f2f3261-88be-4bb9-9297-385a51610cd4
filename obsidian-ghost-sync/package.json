{"name": "obsidian-ghost-sync", "version": "1.0.0", "description": "Sync posts between Obsidian and Ghost.io", "main": "src/main.ts", "scripts": {"dev": "node esbuild.config.mjs", "build": "tsc -noEmit -skipLibCheck && node esbuild.config.mjs production", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "test:legacy": "node test-content-conversion.js", "test:build": "npm test && npm run build", "version": "node version-bump.mjs && git add manifest.json versions.json"}, "keywords": [], "author": "<PERSON>", "license": "MIT", "devDependencies": {"@sveltejs/vite-plugin-svelte": "^5.0.4", "@types/node": "^22.0.0", "@typescript-eslint/eslint-plugin": "5.29.0", "@typescript-eslint/parser": "5.29.0", "@vitest/ui": "^2.1.8", "builtin-modules": "3.3.0", "esbuild": "0.17.3", "esbuild-svelte": "^0.9.3", "jsdom": "^26.0.0", "obsidian": "latest", "svelte": "^5.38.1", "tslib": "2.4.0", "typescript": "4.7.4", "vite": "^6.0.7", "vitest": "^2.1.8"}, "dependencies": {"@tryghost/admin-api": "^1.14.0", "turndown": "^7.2.0"}}