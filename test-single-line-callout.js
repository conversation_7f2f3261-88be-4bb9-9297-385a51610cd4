#!/usr/bin/env node

// Test the single-line callout format
const testMarkdown = `> [!info] Here's a callout title > Here's a callout block.

I wonder if callouts will work?

> [!tip] Here's a tip!`;

console.log('🔄 Testing Single-Line Callout Format\n');

console.log('📝 Input Markdown:');
console.log('─'.repeat(50));
console.log(testMarkdown);
console.log('\n');

// Test the regex patterns
const singleLineRegex = /^> \[!(\w+)\]\s*(.+)$/gm;

console.log('🔍 Testing Regex Matches:');
console.log('─'.repeat(50));

let match;
while ((match = singleLineRegex.exec(testMarkdown)) !== null) {
  console.log(`Found callout: type="${match[1]}", content="${match[2]}"`);
}

console.log('\n✅ If you see callout matches above, the regex is working!');
